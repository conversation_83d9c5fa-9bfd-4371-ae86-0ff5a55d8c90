package com.smilebrands.callcentersupport.util;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * Utility class for exporting Excel files to HTTP response
 */
public class ExcelExporter {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelExporter.class);
    
    /**
     * Writes Excel file content to HTTP response output stream
     * 
     * @param response HttpServletResponse to write to
     * @param filePath Path to the Excel file to export
     * @param fileName Name for the downloaded file
     * @throws IOException if there's an error reading the file or writing to response
     */
    public static void writeExcelToResponse(HttpServletResponse response, String filePath, String fileName) throws IOException {
        if (filePath == null || filePath.trim().isEmpty()) {
            logger.warn("File path is null or empty, cannot export Excel file");
            return;
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            logger.warn("File does not exist: {}", filePath);
            return;
        }
        
        FileInputStream in = null;
        ServletOutputStream out = null;
        
        try {
            // Set response headers before getting output stream
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment; filename=" + (fileName != null ? fileName : file.getName()));
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setContentLength((int) file.length());
            
            // Get output stream AFTER setting headers
            out = response.getOutputStream();
            in = new FileInputStream(file);
            
            // Copy file content to response
            IOUtils.copy(in, out);
            out.flush();
            
            logger.debug("Successfully exported Excel file: {}", fileName);
            
        } catch (IOException e) {
            logger.error("Error exporting Excel file: {}", e.getMessage(), e);
            throw e;
        } finally {
            // Clean up resources
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(out);
            
            // Delete temporary file if it exists
            if (file.exists()) {
                boolean deleted = file.delete();
                if (!deleted) {
                    logger.warn("Could not delete temporary file: {}", filePath);
                }
            }
        }
    }
    
    /**
     * Writes Excel file content to HTTP response output stream with default filename
     * 
     * @param response HttpServletResponse to write to
     * @param filePath Path to the Excel file to export
     * @throws IOException if there's an error reading the file or writing to response
     */
    public static void writeExcelToResponse(HttpServletResponse response, String filePath) throws IOException {
        writeExcelToResponse(response, filePath, null);
    }
}
