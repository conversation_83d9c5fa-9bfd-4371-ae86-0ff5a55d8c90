package com.smilebrands.callcentersupport.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for exporting Excel files to HTTP response
 */
public class ExcelExporter {

    private static final Logger logger = LoggerFactory.getLogger(ExcelExporter.class);

    /**
     * Writes Excel file content to HTTP response output stream
     *
     * @param response HttpServletResponse to write to
     * @param filePath Path to the Excel file to export
     * @param fileName Name for the downloaded file
     * @throws IOException if there's an error reading the file or writing to response
     */
    public static void writeExcelToResponse(HttpServletResponse response, String filePath, String fileName) throws IOException {
        if (filePath == null || filePath.trim().isEmpty()) {
            logger.warn("File path is null or empty, cannot export Excel file");
            sendError(response, "File path is null or empty");
            return;
        }

        File file = new File(filePath);
        if (!file.exists()) {
            logger.warn("File does not exist: {}", filePath);
            sendError(response, "File does not exist: " + filePath);
            return;
        }

        // Check if response is already committed before proceeding
        if (response.isCommitted()) {
            logger.warn("Response already committed, cannot write Excel file");
            return;
        }

        FileInputStream in = null;
        ServletOutputStream out = null;

        try {
            // Set response headers before getting output stream
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment; filename=" + (fileName != null ? fileName : file.getName()));
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setContentLength((int) file.length());

            // Get output stream AFTER setting headers
            out = response.getOutputStream();
            in = new FileInputStream(file);

            // Copy file content to response
            IOUtils.copy(in, out);
            out.flush();

            logger.debug("Successfully exported Excel file: {}", fileName);

        } catch (IOException e) {
            logger.error("Error exporting Excel file: {}", e.getMessage(), e);
            // Try to send error message if response isn't committed
            if (!response.isCommitted()) {
                sendError(response, "Error exporting file: " + e.getMessage());
            }
            throw e;
        } finally {
            // Clean up resources
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(out);

            // Delete temporary file if it exists
            if (file.exists()) {
                boolean deleted = file.delete();
                if (!deleted) {
                    logger.warn("Could not delete temporary file: {}", filePath);
                }
            }
        }
    }

    /**
     * Writes Excel file content to HTTP response output stream with default filename
     *
     * @param response HttpServletResponse to write to
     * @param filePath Path to the Excel file to export
     * @throws IOException if there's an error reading the file or writing to response
     */
    public static void writeExcelToResponse(HttpServletResponse response, String filePath) throws IOException {
        writeExcelToResponse(response, filePath, null);
    }

    /**
     * Sends an error message using the response output stream (not writer)
     * This ensures consistency when getOutputStream() has already been called
     *
     * @param response HttpServletResponse to write to
     * @param message Error message to send
     */
    public static void sendError(HttpServletResponse response, String message) {
        try {
            if (!response.isCommitted()) {
                response.reset(); // clear previous buffer if safe
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("text/plain; charset=UTF-8");

                try {
                    ServletOutputStream out = response.getOutputStream();
                    out.write(message.getBytes(StandardCharsets.UTF_8));
                    out.flush();
                } catch (IllegalStateException streamErr) {
                    // fallback to writer if stream fails
                    logger.warn("OutputStream unavailable, falling back to writer: {}", streamErr.getMessage());
                    response.getWriter().write(message);
                    response.getWriter().flush();
                }
            } else {
                logger.warn("Cannot send error message - response already committed: {}", message);
            }
        } catch (IOException e) {
            logger.error("Error sending error message: " + e.getMessage(), e);
        }
    }
}
