package com.smilestaffing.web.util;

import java.io.IOException;

import javax.servlet.http.HttpServletResponse;

/**
 * Legacy ExcelExporter class for backward compatibility
 * Delegates to the new implementation in com.smilebrands.callcentersupport.util
 *
 * @deprecated Use com.smilebrands.callcentersupport.util.ExcelExporter instead
 */
@Deprecated
public class ExcelExporter {

    /**
     * Writes Excel file content to HTTP response output stream
     *
     * @param response HttpServletResponse to write to
     * @param filePath Path to the Excel file to export
     * @param fileName Name for the downloaded file
     * @throws IOException if there's an error reading the file or writing to response
     * @deprecated Use com.smilebrands.callcentersupport.util.ExcelExporter.writeExcelToResponse instead
     */
    @Deprecated
    public static void writeExcelToResponse(HttpServletResponse response, String filePath, String fileName) throws IOException {
        com.smilebrands.callcentersupport.util.ExcelExporter.writeExcelToResponse(response, filePath, fileName);
    }

    /**
     * Writes Excel file content to HTTP response output stream with default filename
     *
     * @param response HttpServletResponse to write to
     * @param filePath Path to the Excel file to export
     * @throws IOException if there's an error reading the file or writing to response
     * @deprecated Use com.smilebrands.callcentersupport.util.ExcelExporter.writeExcelToResponse instead
     */
    @Deprecated
    public static void writeExcelToResponse(HttpServletResponse response, String filePath) throws IOException {
        com.smilebrands.callcentersupport.util.ExcelExporter.writeExcelToResponse(response, filePath);
    }
}
