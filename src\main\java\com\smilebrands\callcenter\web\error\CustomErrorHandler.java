package com.smilebrands.callcenter.web.error;

import java.io.IOException;
import java.io.Writer;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.eclipse.jetty.http.HttpStatus;
import org.eclipse.jetty.server.Request;
import org.eclipse.jetty.server.handler.ErrorHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Custom error handler for Jetty to better handle incomplete requests
 * and provide more detailed logging.
 */
public class CustomErrorHandler extends ErrorHandler {
    private static final Logger logger = LoggerFactory.getLogger(CustomErrorHandler.class);

    @Override
    public void handle(String target, Request baseRequest, HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        String method = request.getMethod();
        String uri = request.getRequestURI();
        int status = response.getStatus();
        String message = "Unknown"; // HttpServletResponse doesn't have getMessage()

        // Log detailed information about the error
        logger.error("Error handling request: " + method + " " + uri + " - Status: " + status + " " + message);

        // Log additional request details that might help diagnose the issue
        logger.error("Request details: Remote IP: " + request.getRemoteAddr() +
                ", Content-Type: " + request.getContentType() +
                ", Content-Length: " + request.getContentLength() +
                ", User-Agent: " + request.getHeader("User-Agent"));

        // Handle incomplete requests specially
        // Since we can't check the message directly, we'll handle all 500 errors
        // and let our filter do more specific handling
        if (status == HttpStatus.INTERNAL_SERVER_ERROR_500) {
            logger.warn("Detected possible incomplete request. This may be due to a client disconnect or network issue.");
            response.setStatus(HttpStatus.BAD_REQUEST_400);
            response.setContentType("text/html;charset=utf-8");

            Writer writer = null;
            try {
                writer = response.getWriter();
                writer.write("<html><head><title>Incomplete Request</title></head>");
                writer.write("<body><h2>Incomplete Request</h2>");
                writer.write("<p>The server received an incomplete request. This may be due to:</p>");
                writer.write("<ul>");
                writer.write("<li>Client disconnection during request transmission</li>");
                writer.write("<li>Network issues causing partial data transmission</li>");
                writer.write("<li>Proxy or gateway timeout</li>");
                writer.write("</ul>");
                writer.write("<p>Please try again or contact support if the issue persists.</p>");
                writer.write("</body></html>");
            } finally {
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e) {
                        logger.error("Error closing writer", e);
                    }
                }
            }

            baseRequest.setHandled(true);
            return;
        }

        // For other errors, use the default error handler
        super.handle(target, baseRequest, request, response);
    }
}
